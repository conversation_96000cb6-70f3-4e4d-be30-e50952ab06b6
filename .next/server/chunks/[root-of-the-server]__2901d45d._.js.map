{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\nimport { cookies } from \"next/headers\";\n\n/**\n * Especially important if using Fluid compute: Don't put this client in a\n * global variable. Always create a new client within each function when using\n * it.\n */\nexport async function createClient() {\n  const cookieStore = await cookies();\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll();\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options),\n            );\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    },\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAOO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/api/process/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@/lib/supabase/server';\nimport OpenAI from 'openai';\nimport sharp from 'sharp';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check authentication\n    const supabase = await createClient();\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const { imageData, prompt } = await request.json();\n\n    if (!imageData || !prompt) {\n      return NextResponse.json({ \n        error: 'Image data and prompt are required' \n      }, { status: 400 });\n    }\n\n    // Validate prompt length for gpt-image-1\n    if (prompt.length > 32000) {\n      return NextResponse.json({ \n        error: 'Prompt too long. Maximum 32000 characters for gpt-image-1.' \n      }, { status: 400 });\n    }\n\n    // Convert base64 image to buffer and ensure it's in the right format\n    const base64Data = imageData.replace(/^data:image\\/[a-z]+;base64,/, '');\n    const imageBuffer = Buffer.from(base64Data, 'base64');\n\n    // Convert to PNG format using Sharp for better compatibility\n    const pngBuffer = await sharp(imageBuffer)\n      .png()\n      .resize(1024, 1024, { \n        fit: 'inside', \n        withoutEnlargement: true,\n        background: { r: 255, g: 255, b: 255, alpha: 1 }\n      })\n      .toBuffer();\n\n    // Create a proper File stream for OpenAI\n    const imageBlob = new Blob([pngBuffer], { type: 'image/png' });\n    const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });\n\n    console.log('Sending image to OpenAI:', {\n      originalSize: imageBuffer.length,\n      processedSize: pngBuffer.length,\n      prompt: prompt,\n      fileType: imageFile.type\n    });\n\n    // Use gpt-image-1 for image editing\n    console.log('Using gpt-image-1 for image transformation...');\n    const response = await openai.images.edit({\n      model: \"gpt-image-1\",\n      image: imageFile,\n      prompt: prompt,\n      n: 1,\n      size: \"1024x1024\",\n      output_format: \"png\",\n      input_fidelity: \"high\",\n      quality: \"high\"\n    });\n    \n    const modelUsed = \"gpt-image-1\";\n\n    console.log('Model used:', modelUsed);\n    console.log('Response data length:', response.data?.length);\n\n    let editedImageUrl = null;\n    \n    if (response.data && Array.isArray(response.data) && response.data.length > 0) {\n      const firstResult = response.data[0];\n      \n      // gpt-image-1 returns base64-encoded images\n      if (firstResult.b64_json) {\n        // Convert base64 to data URL\n        editedImageUrl = `data:image/png;base64,${firstResult.b64_json}`;\n        console.log('Generated data URL from base64 for gpt-image-1');\n      }\n    }\n\n    if (!editedImageUrl) {\n      console.error('No image data found in OpenAI response. Full response:', JSON.stringify(response, null, 2));\n      throw new Error(`No edited image data found. Model: ${modelUsed}, Response keys: ${response.data?.[0] ? Object.keys(response.data[0]).join(', ') : 'no data'}`);\n    }\n\n    return NextResponse.json({\n      success: true,\n      editedImageUrl,\n      originalPrompt: prompt,\n      processedAt: new Date().toISOString(),\n      model: modelUsed\n    });\n\n  } catch (error) {\n    console.error('Processing error:', error);\n    \n    // Handle specific OpenAI errors\n    if (error instanceof OpenAI.APIError) {\n      if (error.status === 401) {\n        return NextResponse.json({ \n          error: 'Invalid API key configuration' \n        }, { status: 500 });\n      }\n      if (error.status === 429) {\n        return NextResponse.json({ \n          error: 'API rate limit exceeded. Please try again later.' \n        }, { status: 429 });\n      }\n      if (error.status === 400) {\n        return NextResponse.json({ \n          error: 'Invalid image or prompt. Please check your input and try again.' \n        }, { status: 400 });\n      }\n      return NextResponse.json({ \n        error: `API error: ${error.message}` \n      }, { status: error.status || 500 });\n    }\n\n    return NextResponse.json({ \n      error: 'Processing failed. Please try again.' \n    }, { status: 500 });\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEA,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,uBAAuB;QACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEhD,IAAI,CAAC,aAAa,CAAC,QAAQ;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yCAAyC;QACzC,IAAI,OAAO,MAAM,GAAG,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,qEAAqE;QACrE,MAAM,aAAa,UAAU,OAAO,CAAC,+BAA+B;QACpE,MAAM,cAAc,OAAO,IAAI,CAAC,YAAY;QAE5C,6DAA6D;QAC7D,MAAM,YAAY,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,aAC3B,GAAG,GACH,MAAM,CAAC,MAAM,MAAM;YAClB,KAAK;YACL,oBAAoB;YACpB,YAAY;gBAAE,GAAG;gBAAK,GAAG;gBAAK,GAAG;gBAAK,OAAO;YAAE;QACjD,GACC,QAAQ;QAEX,yCAAyC;QACzC,MAAM,YAAY,IAAI,KAAK;YAAC;SAAU,EAAE;YAAE,MAAM;QAAY;QAC5D,MAAM,YAAY,IAAI,KAAK;YAAC;SAAU,EAAE,aAAa;YAAE,MAAM;QAAY;QAEzE,QAAQ,GAAG,CAAC,4BAA4B;YACtC,cAAc,YAAY,MAAM;YAChC,eAAe,UAAU,MAAM;YAC/B,QAAQ;YACR,UAAU,UAAU,IAAI;QAC1B;QAEA,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACxC,OAAO;YACP,OAAO;YACP,QAAQ;YACR,GAAG;YACH,MAAM;YACN,eAAe;YACf,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,YAAY;QAElB,QAAQ,GAAG,CAAC,eAAe;QAC3B,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI,EAAE;QAEpD,IAAI,iBAAiB;QAErB,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;YAC7E,MAAM,cAAc,SAAS,IAAI,CAAC,EAAE;YAEpC,4CAA4C;YAC5C,IAAI,YAAY,QAAQ,EAAE;gBACxB,6BAA6B;gBAC7B,iBAAiB,CAAC,sBAAsB,EAAE,YAAY,QAAQ,EAAE;gBAChE,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,QAAQ,KAAK,CAAC,0DAA0D,KAAK,SAAS,CAAC,UAAU,MAAM;YACvG,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,UAAU,iBAAiB,EAAE,SAAS,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,WAAW;QAChK;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,gBAAgB;YAChB,aAAa,IAAI,OAAO,WAAW;YACnC,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,gCAAgC;QAChC,IAAI,iBAAiB,wKAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;YACpC,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;YACtC,GAAG;gBAAE,QAAQ,MAAM,MAAM,IAAI;YAAI;QACnC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}