{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className,\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from \"@supabase/ssr\";\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/twitter-utils.ts"], "sourcesContent": ["/**\n * Twitter Integration Utilities\n * Handles image downloads, clipboard operations, and Twitter Web Intent integration\n */\n\nexport interface TwitterShareResult {\n  success: boolean;\n  message: string;\n  imageCopied: boolean;\n  imageDownloaded: boolean;\n  textCopied: boolean;\n  twitterOpened: boolean;\n}\n\n/**\n * Downloads an image from a URL to the user's device\n */\nexport const downloadImage = async (imageUrl: string, filename?: string): Promise<boolean> => {\n  try {\n    console.log('🔽 Starting image download:', { imageUrl, filename });\n    \n    const response = await fetch(imageUrl);\n    if (!response.ok) {\n      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);\n    }\n    \n    const blob = await response.blob();\n    console.log('📦 Image blob created:', { size: blob.size, type: blob.type });\n    \n    // Create download link\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename || `tears-of-the-left-${Date.now()}.png`;\n    \n    // Trigger download\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    // Cleanup\n    window.URL.revokeObjectURL(url);\n    \n    console.log('✅ Image download completed successfully');\n    return true;\n  } catch (error) {\n    console.error('❌ Image download failed:', error);\n    return false;\n  }\n};\n\n/**\n * Copies text to clipboard\n */\nexport const copyTextToClipboard = async (text: string): Promise<boolean> => {\n  try {\n    console.log('📋 Copying text to clipboard:', { textLength: text.length });\n    \n    if (navigator.clipboard && window.isSecureContext) {\n      // Modern Clipboard API\n      await navigator.clipboard.writeText(text);\n      console.log('✅ Text copied using Clipboard API');\n      return true;\n    } else {\n      // Fallback for older browsers\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      document.body.appendChild(textArea);\n      textArea.focus();\n      textArea.select();\n      \n      const result = document.execCommand('copy');\n      document.body.removeChild(textArea);\n      \n      if (result) {\n        console.log('✅ Text copied using execCommand fallback');\n        return true;\n      } else {\n        throw new Error('execCommand copy failed');\n      }\n    }\n  } catch (error) {\n    console.error('❌ Text copy failed:', error);\n    return false;\n  }\n};\n\n/**\n * Copies an image to clipboard (modern browsers only)\n */\nexport const copyImageToClipboard = async (imageUrl: string): Promise<boolean> => {\n  try {\n    console.log('🖼️ Copying image to clipboard:', { imageUrl });\n    \n    // Check if Clipboard API is available\n    if (!navigator.clipboard || !window.ClipboardItem) {\n      console.log('⚠️ Clipboard API not available for images');\n      return false;\n    }\n    \n    // Fetch the image\n    const response = await fetch(imageUrl);\n    if (!response.ok) {\n      throw new Error(`Failed to fetch image: ${response.status}`);\n    }\n    \n    const blob = await response.blob();\n    console.log('📦 Image blob for clipboard:', { size: blob.size, type: blob.type });\n    \n    // Create ClipboardItem\n    const clipboardItem = new ClipboardItem({\n      [blob.type]: blob\n    });\n    \n    // Copy to clipboard\n    await navigator.clipboard.write([clipboardItem]);\n    console.log('✅ Image copied to clipboard successfully');\n    return true;\n  } catch (error) {\n    console.error('❌ Image clipboard copy failed:', error);\n    return false;\n  }\n};\n\n/**\n * Opens Twitter Web Intent with prefilled text\n */\nexport const openTwitterIntent = (text: string): boolean => {\n  try {\n    console.log('🐦 Opening Twitter Web Intent:', { textLength: text.length });\n    \n    const encodedText = encodeURIComponent(text);\n    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodedText}`;\n    \n    console.log('🔗 Twitter URL:', twitterUrl);\n    \n    // Open in new window/tab\n    const twitterWindow = window.open(\n      twitterUrl,\n      'twitter-intent',\n      'width=550,height=420,scrollbars=yes,resizable=yes'\n    );\n    \n    if (twitterWindow) {\n      console.log('✅ Twitter intent opened successfully');\n      return true;\n    } else {\n      console.error('❌ Failed to open Twitter intent (popup blocked?)');\n      return false;\n    }\n  } catch (error) {\n    console.error('❌ Twitter intent failed:', error);\n    return false;\n  }\n};\n\n/**\n * Main function to share image and text to Twitter\n * Attempts multiple strategies with fallbacks\n */\nexport const shareToTwitter = async (\n  imageUrl: string,\n  text: string,\n  filename?: string\n): Promise<TwitterShareResult> => {\n  console.log('🚀 Starting Twitter share process:', { imageUrl, text, filename });\n  \n  const result: TwitterShareResult = {\n    success: false,\n    message: '',\n    imageCopied: false,\n    imageDownloaded: false,\n    textCopied: false,\n    twitterOpened: false\n  };\n  \n  try {\n    // Step 1: Try to copy image to clipboard\n    console.log('📋 Step 1: Attempting image clipboard copy...');\n    result.imageCopied = await copyImageToClipboard(imageUrl);\n    \n    // Step 2: Download image as backup\n    console.log('💾 Step 2: Downloading image as backup...');\n    result.imageDownloaded = await downloadImage(imageUrl, filename);\n    \n    // Step 3: Copy text to clipboard (if image copy failed)\n    if (!result.imageCopied) {\n      console.log('📝 Step 3: Copying text to clipboard as fallback...');\n      result.textCopied = await copyTextToClipboard(text);\n    }\n    \n    // Step 4: Open Twitter Web Intent\n    console.log('🐦 Step 4: Opening Twitter Web Intent...');\n    result.twitterOpened = openTwitterIntent(text);\n    \n    // Determine success and message\n    if (result.imageCopied && result.twitterOpened) {\n      result.success = true;\n      result.message = 'Image copied to clipboard! Twitter opened with your text. Just paste the image in your tweet.';\n    } else if (result.imageDownloaded && result.twitterOpened) {\n      result.success = true;\n      if (result.textCopied) {\n        result.message = 'Image downloaded and text copied! Twitter opened. Upload the downloaded image to your tweet.';\n      } else {\n        result.message = 'Image downloaded! Twitter opened with your text. Upload the downloaded image to your tweet.';\n      }\n    } else if (result.twitterOpened) {\n      result.success = true;\n      result.message = 'Twitter opened with your text. Please manually save and upload the image.';\n    } else {\n      result.success = false;\n      result.message = 'Unable to open Twitter. Please copy your text and image manually.';\n    }\n    \n    console.log('🎯 Twitter share result:', result);\n    return result;\n    \n  } catch (error) {\n    console.error('💥 Twitter share process failed:', error);\n    result.success = false;\n    result.message = 'An error occurred while sharing to Twitter. Please try again.';\n    return result;\n  }\n};\n\n/**\n * Validates tweet text length (Twitter's limit is 280 characters)\n */\nexport const validateTweetText = (text: string): { isValid: boolean; remainingChars: number } => {\n  const maxLength = 280;\n  const remainingChars = maxLength - text.length;\n  \n  return {\n    isValid: remainingChars >= 0,\n    remainingChars\n  };\n};\n\n/**\n * Generates suggested hashtags for the image\n */\nexport const getSuggestedHashtags = (): string[] => {\n  return [\n    '#TearsOfTheLeft',\n    '#AIArt',\n    '#ImageTransformation',\n    '#DigitalArt',\n    '#CreativeAI'\n  ];\n};\n\n/**\n * Creates a default tweet text with @CheersToTears mention\n */\nexport const createDefaultTweetText = (): string => {\n  return `Add your post text here!\n\nMade with @CheersToTears`;\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAcM,MAAM,gBAAgB,OAAO,UAAkB;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC,+BAA+B;YAAE;YAAU;QAAS;QAEhE,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACpF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,0BAA0B;YAAE,MAAM,KAAK,IAAI;YAAE,MAAM,KAAK,IAAI;QAAC;QAEzE,uBAAuB;QACvB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,YAAY,CAAC,kBAAkB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QAEjE,mBAAmB;QACnB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,UAAU;QACV,OAAO,GAAG,CAAC,eAAe,CAAC;QAE3B,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAKO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,QAAQ,GAAG,CAAC,iCAAiC;YAAE,YAAY,KAAK,MAAM;QAAC;QAEvE,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,uBAAuB;YACvB,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO;YACL,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;YACtB,SAAS,KAAK,CAAC,GAAG,GAAG;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,KAAK;YACd,SAAS,MAAM;YAEf,MAAM,SAAS,SAAS,WAAW,CAAC;YACpC,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAKO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,QAAQ,GAAG,CAAC,mCAAmC;YAAE;QAAS;QAE1D,sCAAsC;QACtC,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC,OAAO,aAAa,EAAE;YACjD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,kBAAkB;QAClB,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,EAAE;QAC7D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,gCAAgC;YAAE,MAAM,KAAK,IAAI;YAAE,MAAM,KAAK,IAAI;QAAC;QAE/E,uBAAuB;QACvB,MAAM,gBAAgB,IAAI,cAAc;YACtC,CAAC,KAAK,IAAI,CAAC,EAAE;QACf;QAEA,oBAAoB;QACpB,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;YAAC;SAAc;QAC/C,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAKO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,QAAQ,GAAG,CAAC,kCAAkC;YAAE,YAAY,KAAK,MAAM;QAAC;QAExE,MAAM,cAAc,mBAAmB;QACvC,MAAM,aAAa,CAAC,sCAAsC,EAAE,aAAa;QAEzE,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,OAAO,IAAI,CAC/B,YACA,kBACA;QAGF,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO;YACL,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAMO,MAAM,iBAAiB,OAC5B,UACA,MACA;IAEA,QAAQ,GAAG,CAAC,sCAAsC;QAAE;QAAU;QAAM;IAAS;IAE7E,MAAM,SAA6B;QACjC,SAAS;QACT,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,eAAe;IACjB;IAEA,IAAI;QACF,yCAAyC;QACzC,QAAQ,GAAG,CAAC;QACZ,OAAO,WAAW,GAAG,MAAM,qBAAqB;QAEhD,mCAAmC;QACnC,QAAQ,GAAG,CAAC;QACZ,OAAO,eAAe,GAAG,MAAM,cAAc,UAAU;QAEvD,wDAAwD;QACxD,IAAI,CAAC,OAAO,WAAW,EAAE;YACvB,QAAQ,GAAG,CAAC;YACZ,OAAO,UAAU,GAAG,MAAM,oBAAoB;QAChD;QAEA,kCAAkC;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,aAAa,GAAG,kBAAkB;QAEzC,gCAAgC;QAChC,IAAI,OAAO,WAAW,IAAI,OAAO,aAAa,EAAE;YAC9C,OAAO,OAAO,GAAG;YACjB,OAAO,OAAO,GAAG;QACnB,OAAO,IAAI,OAAO,eAAe,IAAI,OAAO,aAAa,EAAE;YACzD,OAAO,OAAO,GAAG;YACjB,IAAI,OAAO,UAAU,EAAE;gBACrB,OAAO,OAAO,GAAG;YACnB,OAAO;gBACL,OAAO,OAAO,GAAG;YACnB;QACF,OAAO,IAAI,OAAO,aAAa,EAAE;YAC/B,OAAO,OAAO,GAAG;YACjB,OAAO,OAAO,GAAG;QACnB,OAAO;YACL,OAAO,OAAO,GAAG;YACjB,OAAO,OAAO,GAAG;QACnB;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,OAAO,GAAG;QACjB,OAAO,OAAO,GAAG;QACjB,OAAO;IACT;AACF;AAKO,MAAM,oBAAoB,CAAC;IAChC,MAAM,YAAY;IAClB,MAAM,iBAAiB,YAAY,KAAK,MAAM;IAE9C,OAAO;QACL,SAAS,kBAAkB;QAC3B;IACF;AACF;AAKO,MAAM,uBAAuB;IAClC,OAAO;QACL;QACA;QACA;QACA;QACA;KACD;AACH;AAKO,MAAM,yBAAyB;IACpC,OAAO,CAAC;;wBAEc,CAAC;AACzB", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/twitter-post-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Loader2, Twitter, Send, Copy, Download } from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport {\n  shareToTwitter,\n  validateTweetText,\n  createDefaultTweetText,\n  type TwitterShareResult\n} from \"@/lib/twitter-utils\";\n\ninterface TwitterPostSectionProps {\n  imageUrl: string;\n  className?: string;\n}\n\nexport function TwitterPostSection({ imageUrl, className }: TwitterPostSectionProps) {\n  const [tweetText, setTweetText] = useState(\"\");\n  const [isSharing, setIsSharing] = useState(false);\n  const [lastShareResult, setLastShareResult] = useState<TwitterShareResult | null>(null);\n\n  // Initialize with default tweet text\n  useEffect(() => {\n    setTweetText(createDefaultTweetText());\n  }, []);\n\n  const { isValid, remainingChars } = validateTweetText(tweetText);\n\n  const handleShare = async () => {\n    console.log('🐦 TwitterPostSection: Starting share process');\n\n    if (!tweetText.trim()) {\n      toast.error(\"Please enter some text for your tweet.\");\n      return;\n    }\n\n    setIsSharing(true);\n    \n    try {\n      const result = await shareToTwitter(\n        imageUrl,\n        tweetText,\n        `tears-of-the-left-${Date.now()}.png`\n      );\n      \n      setLastShareResult(result);\n      \n      if (result.success) {\n        toast.success(result.message, {\n          duration: 5000,\n          action: result.imageCopied ? {\n            label: \"Got it!\",\n            onClick: () => toast.dismiss()\n          } : undefined\n        });\n      } else {\n        toast.error(result.message, {\n          duration: 7000,\n          action: {\n            label: \"Retry\",\n            onClick: () => handleShare()\n          }\n        });\n      }\n    } catch (error) {\n      console.error('❌ TwitterPostSection: Share failed:', error);\n      toast.error(\"Failed to share to Twitter. Please try again.\");\n    } finally {\n      setIsSharing(false);\n    }\n  };\n\n\n\n  const handleCopyText = async () => {\n    try {\n      await navigator.clipboard.writeText(tweetText);\n      toast.success(\"Tweet text copied to clipboard!\");\n    } catch (error) {\n      console.error('Failed to copy text:', error);\n      toast.error(\"Failed to copy text to clipboard.\");\n    }\n  };\n\n  return (\n    <Card className={`border-accent/30 bg-secondary/5 shadow-xl ${className}`}>\n      <CardHeader className=\"pb-4\">\n        <CardTitle className=\"flex items-center gap-2 text-lg\">\n          <Twitter className=\"h-5 w-5 text-accent\" />\n          Share to Twitter\n        </CardTitle>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-4\">\n        {/* Tweet Text Input */}\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center justify-between\">\n            <label htmlFor=\"tweet-text\" className=\"text-sm font-medium text-foreground\">\n              Tweet Text\n            </label>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleCopyText}\n              className=\"h-6 px-2 text-xs\"\n            >\n              <Copy className=\"h-3 w-3 mr-1\" />\n              Copy\n            </Button>\n          </div>\n          \n          <Textarea\n            id=\"tweet-text\"\n            value={tweetText}\n            onChange={(e) => setTweetText(e.target.value)}\n            placeholder=\"What's happening?\"\n            className=\"min-h-[100px] resize-none\"\n            maxLength={300} // Allow a bit over 280 for better UX\n          />\n        </div>\n\n\n\n        {/* Share Button */}\n        <Button\n          onClick={handleShare}\n          disabled={isSharing || !tweetText.trim()}\n          className=\"w-full bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:hover:scale-100 h-14\"\n        >\n          {isSharing ? (\n            <>\n              <Loader2 className=\"h-5 w-5 mr-3 animate-spin\" />\n              Sharing to Twitter...\n            </>\n          ) : (\n            <>\n              <Send className=\"h-5 w-5 mr-3\" />\n              Share to Twitter\n            </>\n          )}\n        </Button>\n\n        {/* Last Share Result Info */}\n        {lastShareResult && (\n          <div className=\"text-xs text-muted-foreground space-y-1 p-3 bg-secondary/20 rounded-lg border border-secondary/30\">\n            <p className=\"font-medium\">Last share attempt:</p>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${lastShareResult.imageCopied ? 'bg-accent' : 'bg-muted-foreground/30'}`} />\n                <span>Image copied</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${lastShareResult.imageDownloaded ? 'bg-accent' : 'bg-muted-foreground/30'}`} />\n                <span>Image downloaded</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${lastShareResult.textCopied ? 'bg-accent' : 'bg-muted-foreground/30'}`} />\n                <span>Text copied</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${lastShareResult.twitterOpened ? 'bg-accent' : 'bg-muted-foreground/30'}`} />\n                <span>Twitter opened</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"text-xs text-white p-3 bg-accent/5 rounded-lg border border-accent/20\">\n          <p className=\"font-medium mb-1 text-white\">How it works:</p>\n          <ol className=\"list-decimal list-inside space-y-1 text-white\">\n            <li>Your image will be copied to clipboard (if supported)</li>\n            <li>Image will be downloaded as backup</li>\n            <li>Twitter will open with your text pre-filled</li>\n            <li>Paste or upload the image in your tweet</li>\n          </ol>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;AAqBO,SAAS,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAA2B;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAElF,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD;IACpC,GAAG,EAAE;IAEL,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;IAEtD,MAAM,cAAc;QAClB,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAChC,UACA,WACA,CAAC,kBAAkB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;YAGvC,mBAAmB;YAEnB,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,EAAE;oBAC5B,UAAU;oBACV,QAAQ,OAAO,WAAW,GAAG;wBAC3B,OAAO;wBACP,SAAS,IAAM,wIAAA,CAAA,QAAK,CAAC,OAAO;oBAC9B,IAAI;gBACN;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,EAAE;oBAC1B,UAAU;oBACV,QAAQ;wBACN,OAAO;wBACP,SAAS,IAAM;oBACjB;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAIA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACvE,8OAAC,yHAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAwB;;;;;;;;;;;;0BAK/C,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAa,WAAU;kDAAsC;;;;;;kDAG5E,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKrC,8OAAC,6HAAA,CAAA,WAAQ;gCACP,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,aAAY;gCACZ,WAAU;gCACV,WAAW;;;;;;;;;;;;kCAOf,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,aAAa,CAAC,UAAU,IAAI;wBACtC,WAAU;kCAET,0BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;oBAOtC,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAc;;;;;;0CAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,WAAW,GAAG,cAAc,0BAA0B;;;;;;0DAC9G,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,eAAe,GAAG,cAAc,0BAA0B;;;;;;0DAClH,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,UAAU,GAAG,cAAc,0BAA0B;;;;;;0DAC7G,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,aAAa,GAAG,cAAc,0BAA0B;;;;;;0DAChH,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAC3C,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Upload, Sparkles, LogOut, Download } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { createClient } from '@/lib/supabase/client';\nimport Image from 'next/image';\nimport { TwitterPostSection } from '@/components/twitter-post-section';\nimport { Toaster } from 'sonner';\n\n// Hardcoded prompt for \"Tears of the left\" effect\nconst HARDCODED_PROMPT = \"Retro cartoon illustration of a sad elderly man in a dark navy suit and teal necktie, large square glasses, single tear rolling down cheek. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture, simple background with soft abstract swirls in tan. Front-facing bust portrait, expressive arched eyebrows and downturned mouth. Clean vector aesthetic, high-resolution\";\n\ninterface ProcessingResult {\n  editedImageUrl: string;\n  originalPrompt: string;\n  processedAt: string;\n  model: string;\n}\n\nexport default function EditorPage() {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [result, setResult] = useState<ProcessingResult | null>(null);\n  const [uploadedImageData, setUploadedImageData] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const router = useRouter();\n  const supabase = createClient();\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut();\n    router.push('/');\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type.startsWith('image/')) {\n        handleFileSelect(file);\n      } else {\n        setError('Please drop an image file');\n      }\n    }\n  };\n\n  const handleFileSelect = async (file: File | null) => {\n    setSelectedFile(file);\n    setError(null);\n    setResult(null);\n    setUploadedImageData(null);\n\n    if (file) {\n      try {\n        const formData = new FormData();\n        formData.append('file', file);\n\n        const response = await fetch('/api/upload', {\n          method: 'POST',\n          body: formData,\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Upload failed');\n        }\n\n        const data = await response.json();\n        setUploadedImageData(data.image);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Upload failed');\n      }\n    }\n  };\n\n  const handleProcess = async () => {\n    if (!uploadedImageData) {\n      setError('Please upload an image first');\n      return;\n    }\n\n    setIsProcessing(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const response = await fetch('/api/process', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          imageData: uploadedImageData,\n          prompt: HARDCODED_PROMPT,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Processing failed');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Processing failed');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const canProcess = selectedFile && !isProcessing;\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <div className=\"sticky top-0 z-50 w-full border-b border-accent/20 bg-background/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-6 py-4 flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 relative\">\n              <Image\n                src=\"/logo.svg\"\n                alt=\"Tears of the Left Logo\"\n                width={40}\n                height={40}\n                className=\"w-full h-full object-contain\"\n              />\n            </div>\n            <h1 className=\"text-2xl font-bold text-foreground\">\n              Tears of the Left\n            </h1>\n          </div>\n          <Button \n            onClick={handleSignOut}\n            variant=\"outline\" \n            size=\"sm\"\n            className=\"flex items-center gap-2 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent transition-all duration-200\"\n          >\n            <LogOut className=\"h-4 w-4\" />\n            Sign Out\n          </Button>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"w-full px-6 py-8\">\n        <div className=\"max-w-4xl mx-auto space-y-8 fade-in\">\n          {/* Upload Section */}\n          <Card \n            className={`border-2 border-dashed transition-all duration-300 hover:shadow-lg ${\n              isDragOver \n                ? 'border-accent bg-accent/10 shadow-lg scale-[1.02]' \n                : 'border-secondary/30 bg-secondary/5'\n            }`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <CardContent className=\"p-8\">\n              {!selectedFile ? (\n                <div className=\"text-center space-y-6\">\n                  <div className=\"mx-auto w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\">\n                    <Upload className={`h-10 w-10 transition-colors ${isDragOver ? 'text-accent' : 'text-muted-foreground'}`} />\n                  </div>\n                  <div className=\"space-y-3\">\n                    <h3 className=\"text-xl font-bold text-foreground\">\n                      Upload your image\n                    </h3>\n                    <p className=\"text-foreground/80 text-base\">\n                      {isDragOver \n                        ? '✨ Drop your image here' \n                        : 'Transform with the \"Tears of the Left\" effect'\n                      }\n                    </p>\n                  </div>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*\"\n                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}\n                    className=\"hidden\"\n                    id=\"file-upload\"\n                    disabled={isProcessing}\n                  />\n                  <Button \n                    asChild \n                    className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200\"\n                    disabled={isProcessing}\n                  >\n                    <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n                      Choose Image\n                    </label>\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center justify-between p-4 bg-accent/10 rounded-lg border border-accent/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center\">\n                        <Upload className=\"h-6 w-6 text-accent\" />\n                      </div>\n                      <div>\n                        <p className=\"font-semibold text-foreground text-base\">\n                          {selectedFile.name}\n                        </p>\n                        <p className=\"text-sm text-foreground/70\">\n                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB • Ready to transform\n                        </p>\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleFileSelect(null)}\n                      disabled={isProcessing}\n                      className=\"border-accent/30 hover:bg-accent/10 text-foreground\"\n                    >\n                      Change\n                    </Button>\n                  </div>\n                  \n                  <Button\n                    onClick={handleProcess}\n                    disabled={!canProcess}\n                    className=\"w-full h-14 text-lg bg-primary hover:bg-primary/90 text-primary-foreground font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]\"\n                    size=\"lg\"\n                  >\n                    {isProcessing ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-3\" />\n                        Creating tears...\n                      </>\n                    ) : (\n                      <>\n                        <Sparkles className=\"h-6 w-6 mr-3\" />\n                        Transform Image\n                      </>\n                    )}\n                  </Button>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Error Display */}\n          {error && (\n            <Card className=\"border-destructive/50 bg-destructive/10 shadow-lg\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center gap-3 text-center justify-center\">\n                  <div className=\"w-8 h-8 bg-destructive/20 rounded-full flex items-center justify-center\">\n                    <span className=\"text-destructive font-bold\">!</span>\n                  </div>\n                  <p className=\"text-destructive font-medium text-base\">{error}</p>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Loading Animation */}\n          {isProcessing && (\n            <Card className=\"border-accent/30 bg-secondary/5 shadow-xl\">\n              <CardContent className=\"p-8\">\n                <div className=\"space-y-8\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold text-foreground mb-3\">\n                      Creating your masterpiece...\n                    </h3>\n                    <p className=\"text-base text-foreground/80\">\n                      The AI is painting tears of emotion onto your image\n                    </p>\n                  </div>\n                  \n                  {/* Enhanced Loading Animation */}\n                  <div className=\"relative rounded-xl overflow-hidden bg-accent/5 h-96 flex items-center justify-center border border-accent/20\">\n                    <div className=\"space-y-6 text-center\">\n                      {/* Animated Tear Drops */}\n                      <div className=\"relative\">\n                        <div className=\"w-20 h-20 mx-auto relative\">\n                          <div className=\"absolute inset-0 rounded-full bg-accent/30 animate-ping opacity-75\"></div>\n                          <div className=\"absolute inset-2 rounded-full bg-accent/50 animate-pulse\"></div>\n                          <div className=\"absolute inset-4 rounded-full bg-accent\"></div>\n                        </div>\n                        \n                        {/* Falling tear drops */}\n                        <div className=\"absolute -left-10 top-10 space-y-3\">\n                          <div className=\"w-3 h-4 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                          <div className=\"w-3 h-4 bg-accent/70 rounded-full animate-bounce opacity-70\" style={{animationDelay: '0.2s'}}></div>\n                          <div className=\"w-3 h-4 bg-accent/50 rounded-full animate-bounce opacity-50\" style={{animationDelay: '0.4s'}}></div>\n                        </div>\n                        \n                        {/* Right side tear drops */}\n                        <div className=\"absolute -right-10 top-10 space-y-3\">\n                          <div className=\"w-3 h-4 bg-accent/60 rounded-full animate-bounce\" style={{animationDelay: '0.3s'}}></div>\n                          <div className=\"w-3 h-4 bg-accent/40 rounded-full animate-bounce opacity-60\" style={{animationDelay: '0.5s'}}></div>\n                        </div>\n                      </div>\n                      \n                      {/* Progress text */}\n                      <div className=\"space-y-4\">\n                        <div className=\"text-lg font-semibold text-foreground\">\n                          Transforming with emotion...\n                        </div>\n                        <div className=\"flex justify-center space-x-2\">\n                          <div className=\"w-3 h-3 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                          <div className=\"w-3 h-3 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-3 h-3 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Result Display */}\n          {result && !isProcessing && (\n            <div className=\"space-y-8\">\n              {/* Header */}\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-foreground mb-3\">\n                  ✨ Your transformed image\n                </h3>\n                <p className=\"text-base text-foreground/80\">\n                  The &ldquo;Tears of the Left&rdquo; effect has been applied\n                </p>\n              </div>\n\n              {/* Main Content Grid */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:items-start\">\n                {/* Image Section */}\n                <Card className=\"border-accent/30 bg-secondary/5 shadow-xl h-full\">\n                  <CardContent className=\"p-8 h-full flex flex-col\">\n                    <div className=\"space-y-6 flex-1 flex flex-col\">\n                      <div className=\"relative rounded-xl overflow-hidden bg-accent/5 border border-accent/20 p-4 flex-1 flex items-center justify-center\">\n                        <Image\n                          src={result.editedImageUrl}\n                          alt=\"Transformed image\"\n                          width={1024}\n                          height={1024}\n                          className=\"w-full h-auto max-h-96 object-contain mx-auto rounded-lg shadow-lg\"\n                          unoptimized={true}\n                        />\n                      </div>\n\n                      <Button\n                        onClick={() => {\n                          const link = document.createElement('a');\n                          link.href = result.editedImageUrl;\n                          link.download = `tears-of-the-left-${Date.now()}.png`;\n                          document.body.appendChild(link);\n                          link.click();\n                          document.body.removeChild(link);\n                        }}\n                        className=\"w-full h-14 bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]\"\n                      >\n                        <Download className=\"h-5 w-5 mr-3\" />\n                        Download Image\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Twitter Section */}\n                <TwitterPostSection\n                  imageUrl={result.editedImageUrl}\n                  className=\"h-full\"\n                />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n      <Toaster position=\"top-right\" richColors />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,kDAAkD;AAClD,MAAM,mBAAmB;AASV,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,gBAAgB;QACpB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,iBAAiB;YACnB,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,qBAAqB;QAErB,IAAI,MAAM;YACR,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,WAAW,MAAM,MAAM,eAAe;oBAC1C,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,qBAAqB,KAAK,KAAK;YACjC,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,mBAAmB;YACtB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW;oBACX,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,gBAAgB,CAAC;IAEpC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;sCAIrD,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,yHAAA,CAAA,OAAI;4BACH,WAAW,CAAC,mEAAmE,EAC7E,aACI,sDACA,sCACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;sCAER,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,CAAC,6BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,4BAA4B,EAAE,aAAa,gBAAgB,yBAAyB;;;;;;;;;;;sDAE1G,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,8OAAC;oDAAE,WAAU;8DACV,aACG,2BACA;;;;;;;;;;;;sDAIR,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;4CACzD,WAAU;4CACV,IAAG;4CACH,UAAU;;;;;;sDAEZ,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,WAAU;4CACV,UAAU;sDAEV,cAAA,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAAiB;;;;;;;;;;;;;;;;yDAM5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,aAAa,IAAI;;;;;;8EAEpB,8OAAC;oEAAE,WAAU;;wEACV,CAAC,aAAa,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;8DAIpD,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,UAAU;oDACV,WAAU;8DACX;;;;;;;;;;;;sDAKH,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;4CACV,MAAK;sDAEJ,6BACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;oDAAgF;;6EAIjG;;kEACE,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;wBAWlD,uBACC,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;sDAE/C,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;wBAO9D,8BACC,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DAGxD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;sDAM9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAIjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAI;;;;;;kFAC3F,8OAAC;wEAAI,WAAU;wEAA8D,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAC3G,8OAAC;wEAAI,WAAU;wEAA8D,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;0EAI7G,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAmD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAChG,8OAAC;wEAAI,WAAU;wEAA8D,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;;;;;;;kEAK/G,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwC;;;;;;0EAGvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAI;;;;;;kFAC3F,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAC7F,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAW5G,UAAU,CAAC,8BACV,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0C;;;;;;sDAGxD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;8CAM9C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KAAK,OAAO,cAAc;gEAC1B,KAAI;gEACJ,OAAO;gEACP,QAAQ;gEACR,WAAU;gEACV,aAAa;;;;;;;;;;;sEAIjB,8OAAC,2HAAA,CAAA,SAAM;4DACL,SAAS;gEACP,MAAM,OAAO,SAAS,aAAa,CAAC;gEACpC,KAAK,IAAI,GAAG,OAAO,cAAc;gEACjC,KAAK,QAAQ,GAAG,CAAC,kBAAkB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;gEACrD,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,KAAK,KAAK;gEACV,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC5B;4DACA,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;sDAQ7C,8OAAC,yIAAA,CAAA,qBAAkB;4CACjB,UAAU,OAAO,cAAc;4CAC/B,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtB,8OAAC,wIAAA,CAAA,UAAO;gBAAC,UAAS;gBAAY,UAAU;;;;;;;;;;;;AAG9C", "debugId": null}}]}