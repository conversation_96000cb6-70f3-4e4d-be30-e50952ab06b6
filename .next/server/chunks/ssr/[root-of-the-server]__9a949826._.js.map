{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className,\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/theme-switcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeSwitcher() from the server but ThemeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/theme-switcher.tsx <module evaluation>\",\n    \"ThemeSwitcher\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/theme-switcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeSwitcher() from the server but ThemeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/theme-switcher.tsx\",\n    \"ThemeSwitcher\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\nimport { cookies } from \"next/headers\";\n\n/**\n * Especially important if using Fluid compute: Don't put this client in a\n * global variable. Always create a new client within each function when using\n * it.\n */\nexport async function createClient() {\n  const cookieStore = await cookies();\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll();\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options),\n            );\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    },\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAOO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport { ThemeSwitcher } from \"@/components/theme-switcher\";\nimport { createClient } from \"@/lib/supabase/server\";\nimport { redirect } from \"next/navigation\";\nimport Image from \"next/image\";\n\nexport default async function Home() {\n  // Check if user is already authenticated\n  const supabase = await createClient();\n  const { data: { user } } = await supabase.auth.getUser();\n  \n  // If authenticated, redirect to editor\n  if (user) {\n    redirect(\"/editor\");\n  }\n\n  return (\n    <main className=\"min-h-screen bg-background flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-md space-y-8 fade-in\">\n        {/* Header */}\n        <div className=\"text-center space-y-4\">\n          <div className=\"flex justify-center mb-4\">\n            <div className=\"w-24 h-24 relative group\">\n              <Image\n                src=\"/logo.svg\"\n                alt=\"Tears of the Left Logo\"\n                width={96}\n                height={96}\n                className=\"w-full h-full object-contain animate-pulse-slow\"\n              />\n              {/* Clockwise Stars Animation */}\n              <div className=\"absolute inset-0 w-full h-full\">\n                {/* Star 1 - 12 o'clock */}\n                <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '0s'}}></div>\n                {/* Star 2 - 1:30 o'clock */}\n                <div className=\"absolute top-1 right-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '0.3s'}}></div>\n                {/* Star 3 - 3 o'clock */}\n                <div className=\"absolute top-1/2 right-0 transform translate-x-1 -translate-y-1/2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '0.6s'}}></div>\n                {/* Star 4 - 4:30 o'clock */}\n                <div className=\"absolute bottom-1 right-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '0.9s'}}></div>\n                {/* Star 5 - 6 o'clock */}\n                <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '1.2s'}}></div>\n                {/* Star 6 - 7:30 o'clock */}\n                <div className=\"absolute bottom-1 left-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '1.5s'}}></div>\n                {/* Star 7 - 9 o'clock */}\n                <div className=\"absolute top-1/2 left-0 transform -translate-x-1 -translate-y-1/2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '1.8s'}}></div>\n                {/* Star 8 - 10:30 o'clock */}\n                <div className=\"absolute top-1 left-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0\" style={{animationDelay: '2.1s'}}></div>\n              </div>\n            </div>\n          </div>\n          <h1 className=\"text-4xl font-bold text-foreground\">\n            Tears of the Left\n          </h1>\n          <p className=\"text-secondary-foreground\">\n            Transform your images with AI\n          </p>\n        </div>\n\n        {/* Auth Forms */}\n        <div className=\"space-y-6\">\n          <AuthTabs />\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-center\">\n          <ThemeSwitcher />\n        </div>\n      </div>\n    </main>\n  );\n}\n\nfunction AuthTabs() {\n  return (\n    <div className=\"space-y-4\">\n      <Card className=\"border-accent/30 bg-card/90 shadow-xl backdrop-blur-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-center text-xl font-bold text-foreground\">Welcome</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Button asChild variant=\"outline\" className=\"h-12 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent text-foreground font-semibold transition-all duration-200 hover:scale-105\">\n              <a href=\"/auth/login\">Sign In</a>\n            </Button>\n            <Button asChild className=\"h-12 bg-primary hover:bg-primary/80 text-primary-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 transform\">\n              <a href=\"/auth/sign-up\">Sign Up</a>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;AAEe,eAAe;IAC5B,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAEtD,uCAAuC;IACvC,IAAI,MAAM;QACR,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;gDAAiI,OAAO;oDAAC,gBAAgB;gDAAI;;;;;;0DAE5K,8OAAC;gDAAI,WAAU;gDAAsF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAEnI,8OAAC;gDAAI,WAAU;gDAAiI,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAE9K,8OAAC;gDAAI,WAAU;gDAAyF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAEtI,8OAAC;gDAAI,WAAU;gDAAmI,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAEhL,8OAAC;gDAAI,WAAU;gDAAwF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAErI,8OAAC;gDAAI,WAAU;gDAAiI,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAE9K,8OAAC;gDAAI,WAAU;gDAAqF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;;;;;;;;;;;;sCAIxI,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;8BAM3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;;;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;AAKxB;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,yHAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;kCAAgD;;;;;;;;;;;8BAEvE,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,WAAU;0CAC1C,cAAA,8OAAC;oCAAE,MAAK;8CAAc;;;;;;;;;;;0CAExB,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,WAAU;0CACxB,cAAA,8OAAC;oCAAE,MAAK;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}]}