"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Twitter, Send, Copy, Download } from "lucide-react";
import { toast } from "sonner";
import {
  shareToTwitter,
  validateTweetText,
  createDefaultTweetText,
  getSuggestedHashtags,
  type TwitterShareResult
} from "@/lib/twitter-utils";

interface TwitterPostSectionProps {
  imageUrl: string;
  className?: string;
}

export function TwitterPostSection({ imageUrl, className }: TwitterPostSectionProps) {
  const [tweetText, setTweetText] = useState("");
  const [isSharing, setIsSharing] = useState(false);
  const [lastShareResult, setLastShareResult] = useState<TwitterShareResult | null>(null);

  // Initialize with default tweet text
  useEffect(() => {
    setTweetText(createDefaultTweetText());
  }, []);

  const { isValid, remainingChars } = validateTweetText(tweetText);
  const suggestedHashtags = getSuggestedHashtags();

  const handleShare = async () => {
    console.log('🐦 TwitterPostSection: Starting share process');
    
    if (!isValid) {
      toast.error("Tweet text is too long. Please shorten it to 280 characters or less.");
      return;
    }

    if (!tweetText.trim()) {
      toast.error("Please enter some text for your tweet.");
      return;
    }

    setIsSharing(true);
    
    try {
      const result = await shareToTwitter(
        imageUrl,
        tweetText,
        `tears-of-the-left-${Date.now()}.png`
      );
      
      setLastShareResult(result);
      
      if (result.success) {
        toast.success(result.message, {
          duration: 5000,
          action: result.imageCopied ? {
            label: "Got it!",
            onClick: () => toast.dismiss()
          } : undefined
        });
      } else {
        toast.error(result.message, {
          duration: 7000,
          action: {
            label: "Retry",
            onClick: () => handleShare()
          }
        });
      }
    } catch (error) {
      console.error('❌ TwitterPostSection: Share failed:', error);
      toast.error("Failed to share to Twitter. Please try again.");
    } finally {
      setIsSharing(false);
    }
  };

  const handleHashtagClick = (hashtag: string) => {
    const newText = tweetText.includes(hashtag) 
      ? tweetText.replace(hashtag, '').replace(/\s+/g, ' ').trim()
      : `${tweetText} ${hashtag}`.trim();
    
    setTweetText(newText);
  };

  const handleCopyText = async () => {
    try {
      await navigator.clipboard.writeText(tweetText);
      toast.success("Tweet text copied to clipboard!");
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error("Failed to copy text to clipboard.");
    }
  };

  return (
    <Card className={`border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/20 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Twitter className="h-5 w-5 text-blue-500" />
          Share to Twitter
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Tweet Text Input */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor="tweet-text" className="text-sm font-medium text-foreground">
              Tweet Text
            </label>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyText}
                className="h-6 px-2 text-xs"
              >
                <Copy className="h-3 w-3 mr-1" />
                Copy
              </Button>
              <span className={`text-xs font-mono ${
                remainingChars < 0 
                  ? 'text-red-500' 
                  : remainingChars < 20 
                    ? 'text-orange-500' 
                    : 'text-muted-foreground'
              }`}>
                {remainingChars}
              </span>
            </div>
          </div>
          
          <Textarea
            id="tweet-text"
            value={tweetText}
            onChange={(e) => setTweetText(e.target.value)}
            placeholder="What's happening?"
            className={`min-h-[100px] resize-none ${
              !isValid ? 'border-red-500 focus:border-red-500' : ''
            }`}
            maxLength={300} // Allow a bit over 280 for better UX
          />
          
          {!isValid && (
            <p className="text-xs text-red-500">
              Tweet is {Math.abs(remainingChars)} characters too long
            </p>
          )}
        </div>

        {/* Suggested Hashtags */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Suggested Hashtags
          </label>
          <div className="flex flex-wrap gap-2">
            {suggestedHashtags.map((hashtag) => (
              <Badge
                key={hashtag}
                variant={tweetText.includes(hashtag) ? "default" : "outline"}
                className="cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors"
                onClick={() => handleHashtagClick(hashtag)}
              >
                {hashtag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Share Button */}
        <Button
          onClick={handleShare}
          disabled={isSharing || !isValid || !tweetText.trim()}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 h-auto transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:hover:scale-100"
        >
          {isSharing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Sharing to Twitter...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Share to Twitter
            </>
          )}
        </Button>

        {/* Last Share Result Info */}
        {lastShareResult && (
          <div className="text-xs text-muted-foreground space-y-1 p-3 bg-muted/50 rounded-lg">
            <p className="font-medium">Last share attempt:</p>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${lastShareResult.imageCopied ? 'bg-green-500' : 'bg-gray-300'}`} />
                <span>Image copied</span>
              </div>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${lastShareResult.imageDownloaded ? 'bg-green-500' : 'bg-gray-300'}`} />
                <span>Image downloaded</span>
              </div>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${lastShareResult.textCopied ? 'bg-green-500' : 'bg-gray-300'}`} />
                <span>Text copied</span>
              </div>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${lastShareResult.twitterOpened ? 'bg-green-500' : 'bg-gray-300'}`} />
                <span>Twitter opened</span>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-muted-foreground p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="font-medium mb-1">How it works:</p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Your image will be copied to clipboard (if supported)</li>
            <li>Image will be downloaded as backup</li>
            <li>Twitter will open with your text pre-filled</li>
            <li>Paste or upload the image in your tweet</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
