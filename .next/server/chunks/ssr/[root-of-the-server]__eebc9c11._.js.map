{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className,\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from \"@supabase/ssr\";\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Upload, Sparkles, LogOut, Download } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { createClient } from '@/lib/supabase/client';\nimport Image from 'next/image';\n\n// Hardcoded prompt for \"Tears of the left\" effect\nconst HARDCODED_PROMPT = \"Retro cartoon illustration of a sad elderly man in a dark navy suit and teal necktie, large square glasses, single tear rolling down cheek. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture, simple background with soft abstract swirls in tan. Front-facing bust portrait, expressive arched eyebrows and downturned mouth. Clean vector aesthetic, high-resolution\";\n\ninterface ProcessingResult {\n  editedImageUrl: string;\n  originalPrompt: string;\n  processedAt: string;\n  model: string;\n}\n\nexport default function EditorPage() {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [result, setResult] = useState<ProcessingResult | null>(null);\n  const [uploadedImageData, setUploadedImageData] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const router = useRouter();\n  const supabase = createClient();\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut();\n    router.push('/');\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type.startsWith('image/')) {\n        handleFileSelect(file);\n      } else {\n        setError('Please drop an image file');\n      }\n    }\n  };\n\n  const handleFileSelect = async (file: File | null) => {\n    setSelectedFile(file);\n    setError(null);\n    setResult(null);\n    setUploadedImageData(null);\n\n    if (file) {\n      try {\n        const formData = new FormData();\n        formData.append('file', file);\n\n        const response = await fetch('/api/upload', {\n          method: 'POST',\n          body: formData,\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Upload failed');\n        }\n\n        const data = await response.json();\n        setUploadedImageData(data.image);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Upload failed');\n      }\n    }\n  };\n\n  const handleProcess = async () => {\n    if (!uploadedImageData) {\n      setError('Please upload an image first');\n      return;\n    }\n\n    setIsProcessing(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const response = await fetch('/api/process', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          imageData: uploadedImageData,\n          prompt: HARDCODED_PROMPT,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Processing failed');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Processing failed');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const canProcess = selectedFile && !isProcessing;\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <div className=\"sticky top-0 z-50 w-full border-b border-accent/20 bg-background/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-6 py-4 flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 relative\">\n              <Image\n                src=\"/logo.svg\"\n                alt=\"Tears of the Left Logo\"\n                width={40}\n                height={40}\n                className=\"w-full h-full object-contain\"\n              />\n            </div>\n            <h1 className=\"text-2xl font-bold text-foreground\">\n              Tears of the Left\n            </h1>\n          </div>\n          <Button \n            onClick={handleSignOut}\n            variant=\"outline\" \n            size=\"sm\"\n            className=\"flex items-center gap-2 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent transition-all duration-200\"\n          >\n            <LogOut className=\"h-4 w-4\" />\n            Sign Out\n          </Button>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"w-full px-6 py-8\">\n        <div className=\"max-w-4xl mx-auto space-y-8 fade-in\">\n          {/* Upload Section */}\n          <Card \n            className={`border-2 border-dashed transition-all duration-300 hover:shadow-lg ${\n              isDragOver \n                ? 'border-accent bg-accent/10 shadow-lg scale-[1.02]' \n                : 'border-secondary/30 bg-secondary/5'\n            }`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <CardContent className=\"p-8\">\n              {!selectedFile ? (\n                <div className=\"text-center space-y-6\">\n                  <div className=\"mx-auto w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110\">\n                    <Upload className={`h-10 w-10 transition-colors ${isDragOver ? 'text-accent' : 'text-muted-foreground'}`} />\n                  </div>\n                  <div className=\"space-y-3\">\n                    <h3 className=\"text-xl font-bold text-foreground\">\n                      Upload your image\n                    </h3>\n                    <p className=\"text-foreground/80 text-base\">\n                      {isDragOver \n                        ? '✨ Drop your image here' \n                        : 'Transform with the \"Tears of the Left\" effect'\n                      }\n                    </p>\n                  </div>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*\"\n                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}\n                    className=\"hidden\"\n                    id=\"file-upload\"\n                    disabled={isProcessing}\n                  />\n                  <Button \n                    asChild \n                    className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200\"\n                    disabled={isProcessing}\n                  >\n                    <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n                      Choose Image\n                    </label>\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center justify-between p-4 bg-accent/10 rounded-lg border border-accent/20\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center\">\n                        <Upload className=\"h-6 w-6 text-accent\" />\n                      </div>\n                      <div>\n                        <p className=\"font-semibold text-foreground text-base\">\n                          {selectedFile.name}\n                        </p>\n                        <p className=\"text-sm text-foreground/70\">\n                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB • Ready to transform\n                        </p>\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleFileSelect(null)}\n                      disabled={isProcessing}\n                      className=\"border-accent/30 hover:bg-accent/10 text-foreground\"\n                    >\n                      Change\n                    </Button>\n                  </div>\n                  \n                  <Button\n                    onClick={handleProcess}\n                    disabled={!canProcess}\n                    className=\"w-full h-14 text-lg bg-primary hover:bg-primary/90 text-primary-foreground font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]\"\n                    size=\"lg\"\n                  >\n                    {isProcessing ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-3\" />\n                        Creating tears...\n                      </>\n                    ) : (\n                      <>\n                        <Sparkles className=\"h-6 w-6 mr-3\" />\n                        Transform Image\n                      </>\n                    )}\n                  </Button>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Error Display */}\n          {error && (\n            <Card className=\"border-destructive/50 bg-destructive/10 shadow-lg\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center gap-3 text-center justify-center\">\n                  <div className=\"w-8 h-8 bg-destructive/20 rounded-full flex items-center justify-center\">\n                    <span className=\"text-destructive font-bold\">!</span>\n                  </div>\n                  <p className=\"text-destructive font-medium text-base\">{error}</p>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Loading Animation */}\n          {isProcessing && (\n            <Card className=\"border-accent/30 bg-secondary/5 shadow-xl\">\n              <CardContent className=\"p-8\">\n                <div className=\"space-y-8\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold text-foreground mb-3\">\n                      Creating your masterpiece...\n                    </h3>\n                    <p className=\"text-base text-foreground/80\">\n                      The AI is painting tears of emotion onto your image\n                    </p>\n                  </div>\n                  \n                  {/* Enhanced Loading Animation */}\n                  <div className=\"relative rounded-xl overflow-hidden bg-accent/5 h-96 flex items-center justify-center border border-accent/20\">\n                    <div className=\"space-y-6 text-center\">\n                      {/* Animated Tear Drops */}\n                      <div className=\"relative\">\n                        <div className=\"w-20 h-20 mx-auto relative\">\n                          <div className=\"absolute inset-0 rounded-full bg-accent/30 animate-ping opacity-75\"></div>\n                          <div className=\"absolute inset-2 rounded-full bg-accent/50 animate-pulse\"></div>\n                          <div className=\"absolute inset-4 rounded-full bg-accent\"></div>\n                        </div>\n                        \n                        {/* Falling tear drops */}\n                        <div className=\"absolute -left-10 top-10 space-y-3\">\n                          <div className=\"w-3 h-4 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                          <div className=\"w-3 h-4 bg-accent/70 rounded-full animate-bounce opacity-70\" style={{animationDelay: '0.2s'}}></div>\n                          <div className=\"w-3 h-4 bg-accent/50 rounded-full animate-bounce opacity-50\" style={{animationDelay: '0.4s'}}></div>\n                        </div>\n                        \n                        {/* Right side tear drops */}\n                        <div className=\"absolute -right-10 top-10 space-y-3\">\n                          <div className=\"w-3 h-4 bg-accent/60 rounded-full animate-bounce\" style={{animationDelay: '0.3s'}}></div>\n                          <div className=\"w-3 h-4 bg-accent/40 rounded-full animate-bounce opacity-60\" style={{animationDelay: '0.5s'}}></div>\n                        </div>\n                      </div>\n                      \n                      {/* Progress text */}\n                      <div className=\"space-y-4\">\n                        <div className=\"text-lg font-semibold text-foreground\">\n                          Transforming with emotion...\n                        </div>\n                        <div className=\"flex justify-center space-x-2\">\n                          <div className=\"w-3 h-3 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                          <div className=\"w-3 h-3 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-3 h-3 bg-accent rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Result Display */}\n          {result && !isProcessing && (\n            <Card className=\"border-accent/30 bg-secondary/5 shadow-xl\">\n              <CardContent className=\"p-8\">\n                <div className=\"space-y-8\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold text-foreground mb-3\">\n                      ✨ Your transformed image\n                    </h3>\n                    <p className=\"text-base text-foreground/80\">\n                      The &ldquo;Tears of the Left&rdquo; effect has been applied\n                    </p>\n                  </div>\n                  \n                  <div className=\"relative rounded-xl overflow-hidden bg-accent/5 border border-accent/20 p-4\">\n                    <Image\n                      src={result.editedImageUrl}\n                      alt=\"Transformed image\"\n                      width={1024}\n                      height={1024}\n                      className=\"w-full h-auto max-h-96 object-contain mx-auto rounded-lg shadow-lg\"\n                      unoptimized={true}\n                    />\n                  </div>\n                  \n                  <Button\n                    onClick={() => {\n                      const link = document.createElement('a');\n                      link.href = result.editedImageUrl;\n                      link.download = `tears-of-the-left-${Date.now()}.png`;\n                      document.body.appendChild(link);\n                      link.click();\n                      document.body.removeChild(link);\n                    }}\n                    className=\"w-full h-14 bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]\"\n                  >\n                    <Download className=\"h-5 w-5 mr-3\" />\n                    Download Image\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,kDAAkD;AAClD,MAAM,mBAAmB;AASV,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,gBAAgB;QACpB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,iBAAiB;YACnB,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,qBAAqB;QAErB,IAAI,MAAM;YACR,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,WAAW,MAAM,MAAM,eAAe;oBAC1C,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,qBAAqB,KAAK,KAAK;YACjC,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,mBAAmB;YACtB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW;oBACX,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,gBAAgB,CAAC;IAEpC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;sCAIrD,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,yHAAA,CAAA,OAAI;4BACH,WAAW,CAAC,mEAAmE,EAC7E,aACI,sDACA,sCACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;sCAER,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,CAAC,6BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,4BAA4B,EAAE,aAAa,gBAAgB,yBAAyB;;;;;;;;;;;sDAE1G,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,8OAAC;oDAAE,WAAU;8DACV,aACG,2BACA;;;;;;;;;;;;sDAIR,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;4CACzD,WAAU;4CACV,IAAG;4CACH,UAAU;;;;;;sDAEZ,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,WAAU;4CACV,UAAU;sDAEV,cAAA,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAAiB;;;;;;;;;;;;;;;;yDAM5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,aAAa,IAAI;;;;;;8EAEpB,8OAAC;oEAAE,WAAU;;wEACV,CAAC,aAAa,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;8DAIpD,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,UAAU;oDACV,WAAU;8DACX;;;;;;;;;;;;sDAKH,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;4CACV,MAAK;sDAEJ,6BACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;oDAAgF;;6EAIjG;;kEACE,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;wBAWlD,uBACC,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;sDAE/C,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;wBAO9D,8BACC,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DAGxD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;sDAM9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAIjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAI;;;;;;kFAC3F,8OAAC;wEAAI,WAAU;wEAA8D,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAC3G,8OAAC;wEAAI,WAAU;wEAA8D,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;0EAI7G,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAmD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAChG,8OAAC;wEAAI,WAAU;wEAA8D,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;;;;;;;kEAK/G,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwC;;;;;;0EAGvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAI;;;;;;kFAC3F,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;kFAC7F,8OAAC;wEAAI,WAAU;wEAAgD,OAAO;4EAAC,gBAAgB;wEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAW5G,UAAU,CAAC,8BACV,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DAGxD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;sDAK9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,OAAO,cAAc;gDAC1B,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,aAAa;;;;;;;;;;;sDAIjB,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAS;gDACP,MAAM,OAAO,SAAS,aAAa,CAAC;gDACpC,KAAK,IAAI,GAAG,OAAO,cAAc;gDACjC,KAAK,QAAQ,GAAG,CAAC,kBAAkB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;gDACrD,SAAS,IAAI,CAAC,WAAW,CAAC;gDAC1B,KAAK,KAAK;gDACV,SAAS,IAAI,CAAC,WAAW,CAAC;4CAC5B;4CACA,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD", "debugId": null}}]}