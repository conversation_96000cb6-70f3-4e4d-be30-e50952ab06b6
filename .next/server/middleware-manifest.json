{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_35c8d03a._.js", "server/edge/chunks/[root-of-the-server]__b8e15c56._.js", "server/edge/chunks/edge-wrapper_6068de0d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UiH8fJZxArHLaH6Q65VYNDLXP79LSp17ZAoOb5d86m0=", "__NEXT_PREVIEW_MODE_ID": "f40e5d7bbc16fc798d698d76eb79a0dc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d7c8bdd52be4148c7a826c5c4510e04552f70c2cc4611eefda8853bb37588ac2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "302dd6231994bc7daea9ca760a4c1ad3de0b008a46d0ee25c8b34a02ce8c1ee9"}}}, "sortedMiddleware": ["/"], "functions": {}}